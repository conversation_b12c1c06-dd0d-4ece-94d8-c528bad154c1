<template>
  <div ref="pageContainerRef" class="change-management-main">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="page-title">
        <div class="title-icon"></div>
        <span>变更管理</span>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-section">
      <im-search-form
        class="search-form"
        :columns="state.columnSearchOptions"
        :base-height="28"
        :col-height="40"
        :selectWidth="'233px'"
        :search-style="{ width: '50%', borderRadius: '1px' }"
        :col-num-per-row="3"
        @on-search="searchHandler"
        @on-height-change="handleFormHeightChange"
        @on-expanded-change="handleSearchFormToggle"
        default-values-on-reset
        search-on-expand
      />
    </div>

    <!-- 左侧项目列表 -->
    <div class="content-layout">
      <div class="tree-section" :style="{ width: leftWidth + 'px' }">
        <div class="tree-header">
          <div class="tree-title">项目列表</div>
          <el-button type="primary" size="small" link @click="handleTreeReset">
            重置
          </el-button>
        </div>
        <div class="tree-container">
          <el-tree
            ref="treeRef"
            :data="treeData"
            :props="treeProps"
            node-key="id"
            :expand-on-click-node="false"
            highlight-current
            :default-expanded-keys="['root']"
            @current-change="handleTreeNodeClick"
          >
            <template #default="{ node, data }">
              <div class="tree-node-content">
                <svg
                  style="margin-right: 8px"
                  viewBox="0 0 16 16"
                  width="16"
                  height="16"
                >
                  <path
                    :d="`${
                      data.isLeaf
                        ? 'M13,6 L9,6 L9,5 L9,2 L3,2 L3,14 L13,14 L13,6 Z M12.5857864,5 L10,2.41421356 L10,5 L12.5857864,5 Z M2,1 L10,1 L14,5 L14,15 L2,15 L2,1 Z'
                        : 'M16,6 L14,14 L2,14 L0,6 L16,6 Z M14.7192236,7 L1.28077641,7 L2.78077641,13 L13.2192236,13 L14.7192236,7 Z M6,1 L8,3 L15,3 L15,5 L14,5 L14,4 L7.58578644,4 L5.58578644,2 L2,2 L2,5 L1,5 L1,1 L6,1 Z'
                    }`"
                    stroke-width="1"
                    fill="#8a8e99"
                  ></path>
                </svg>
                <span class="tree-node-label">{{ node.label }}</span>
                <span v-if="data.count !== undefined" class="tree-node-count">({{ data.count }})</span>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 拖拽分隔条 -->
      <div class="resize-handle" @mousedown="handleResizeStart"></div>

      <!-- 右侧变更表格 -->
      <div class="right-content">
        <div class="table-section">
          <im-table
            ref="tableRef"
            :data="state.tableData"
            :columns="tableColumns"
            :loading="state.tableLoading"
            :pagination="state.tablePage"
            :height="tableHeight"
            :column-storage="createColumnStorage('change_management', 'local')"
            stripe
            border
            center
            show-checkbox
            show-overflow-tooltip
            toolbar
            @on-page-change="handlePageChange"
            @on-page-size-change="handlePageSizeChange"
            @on-reload="refreshTableData"
            @selection-change="rowSelectHandler"
            @sort-change="sortChangeHandler"
          >
            <!-- 工具栏左侧 -->
            <template #toolbar-left>
              <el-button
                type="primary"
                size="small"
                @click="handleAddChange"
              >
                新建变更
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="handleFlowConfig"
              >
                变更流转配置
              </el-button>
              <!-- <el-button
                size="small"
                @click="handleExportChange"
              >
                导出
              </el-button> -->
            </template>

            <!-- 状态列（如果需要显示状态） -->
            <template #status="{ row }">
              <el-tag
                :type="getStatusTagType(row.status)"
                size="small"
              >
                {{ row.status }}
              </el-tag>
            </template>

            <!-- 操作列 -->
            <template #operation="{ row }">
              <el-button
                type="primary"
                size="small"
                link
                @click="handleOperation(row)"
              >
                详情
              </el-button>
            </template>
          </im-table>
        </div>
      </div>
    </div>

    <!-- 变更流转配置弹窗 -->
    <ChangeFlowConfigDialog
      v-model="showFlowConfigDialog"
      @save="handleFlowConfigSave"
    />

    <!-- 新建变更弹窗 -->
    <el-dialog
      v-model="showAddDialog"
      title="新建项目"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="项目名称" prop="projectName">
          <el-select
            v-model="addForm.projectName"
            placeholder="请选择"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="project in projectOptions"
              :key="project.value"
              :label="project.label"
              :value="project.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目编号" prop="projectCode">
          <el-select
            v-model="addForm.projectCode"
            placeholder="请选择"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="project in projectCodeOptions"
              :key="project.value"
              :label="project.label"
              :value="project.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelAdd">取消</el-button>
          <el-button type="primary" @click="handleConfirmAdd">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, onUnmounted, getCurrentInstance, nextTick } from "vue";
import { ImTableInstance, createColumnStorage } from "@/components/ItsmCommon";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import {
  mockQueryConditions,
  getProjectTreeData,
  getChangeListData,
  mockProjectOptions,
  type ChangeData,
  type ProjectTreeNode,
  type ChangeQueryParams
} from '../api/index';
import ChangeFlowConfigDialog from './ChangeFlowConfigDialog.vue';

const { $message } = getCurrentInstance().appContext.config.globalProperties;

// 定义事件
const emit = defineEmits(["jump-to"]);

// 表格引用
const tableRef = ref<ImTableInstance>();

// 页面容器引用
const pageContainerRef = ref<HTMLDivElement>();

// 根据页面高度设置表格高度（使用响应式状态）
const tableHeight = computed(() => state.tableHeight);

// 数据对象
const state = reactive({
  columnSearchOptions: [],
  tableData: [] as ChangeData[],
  tableLoading: false,
  tablePage: {
    hideOnEmptyData: true,
    total: 0,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  conditions: [],
  remoteSort: {} as any,
  selectedRows: [] as ChangeData[],
  searchFormHeight: 32,
  tableHeight: 400, // 添加表格高度状态
});

// 左侧面板宽度（参考DocumentManage的实现）
const leftWidth = ref(280);

// 树形组件相关（完全复制DocumentManage的实现）
const treeRef = ref();
const selectedProjectId = ref("root"); // 当前选中的项目ID
const treeData = ref<ProjectTreeNode[]>([]);

// 树组件配置（与DocumentManage保持一致）
const treeProps = {
  label: "label",
  children: "children"
};

// 变更流转配置弹窗相关
const showFlowConfigDialog = ref(false);

// 新建变更弹窗相关
const showAddDialog = ref(false);
const addFormRef = ref();
const addForm = reactive({
  projectName: '',
  projectCode: ''
});

const addFormRules = {
  projectName: [
    { required: true, message: '请选择项目名称', trigger: 'change' }
  ],
  projectCode: [
    { required: true, message: '请选择项目编号', trigger: 'change' }
  ]
};

// 项目选项数据
const projectOptions = ref(mockProjectOptions.projectNames);
const projectCodeOptions = ref(mockProjectOptions.projectCodes);

// 表格列配置
const tableColumns = computed(() => [
  {
    prop: "workOrderCode",
    label: "工单编号",
    minWidth: 150,
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: "workOrderTitle",
    label: "工单标题",
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: "changeOrderCode",
    label: "变更单编号",
    minWidth: 150,
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: "projectName",
    label: "项目名称",
    minWidth: 180,
    showOverflowTooltip: true
  },
  {
    prop: "specialty",
    label: "专业",
    minWidth: 120,
    showOverflowTooltip: true
  },
  {
    prop: "contractName",
    label: "合同名称",
    minWidth: 180,
    showOverflowTooltip: true
  },
  {
    prop: "contractCode",
    label: "合同编号",
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    prop: "currentStage",
    label: "当前环节",
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    prop: "creator",
    label: "创建人",
    minWidth: 120,
    showOverflowTooltip: true
  },
  {
    prop: "createTime",
    label: "创建时间",
    minWidth: 160,
    sortable: true
  },
  {
    prop: "operation",
    label: "操作",
    minWidth: 100,
    fixed: "right",
    slotName: "operation"
  }
]);

// 状态标签类型
const getStatusTagType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const statusMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    '合规': 'success',
    '待审': 'warning',
    '异常': 'danger'
  };
  return statusMap[status] || 'info';
};

// 搜索处理
const searchHandler = (conditions: any[]) => {
  console.log("搜索条件:", conditions);
  state.conditions = conditions;
  state.tablePage.currentPage = 1;
  loadTableData();
};

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 防抖的高度重新计算函数
const debouncedRecalculateHeight = debounce(() => {
  state.tableHeight = calculatePreciseTableHeight();
}, 150);

// 表单高度变化处理
const handleFormHeightChange = (height: number) => {
  console.log('搜索表单高度变化:', `${state.searchFormHeight} -> ${height}`);

  // 只有当高度真正发生变化时才重新计算
  if (state.searchFormHeight !== height) {
    state.searchFormHeight = height;

    // 使用防抖重新计算表格高度
    debouncedRecalculateHeight();
  }
};

// 分页处理 - 修复参数顺序，参考其他组件的正确实现
const handlePageSizeChange = (currentPage: number, size: number, query?: any) => {
  console.log('ChangeManagement - 分页大小变化:', { currentPage, size, query });
  state.tablePage.pageSize = size;
  state.tablePage.currentPage = 1; // 分页大小变化时重置到第一页
  loadTableData();
};

// 添加页码变化处理函数
const handlePageChange = (pageOrSize: number, pageSize: number, query?: any) => {
  console.log('ChangeManagement - 分页变化:', { pageOrSize, pageSize, query });
  // 根据im-table源码，这个事件会在页码变化和分页大小变化时都触发
  // 判断是页码变化还是分页大小变化
  if (pageOrSize !== state.tablePage.pageSize) {
    // 页码变化
    console.log('ChangeManagement - 页码变化:', pageOrSize);
    state.tablePage.currentPage = pageOrSize;
    loadTableData();
  } else {
    // 分页大小变化，但不在这里处理，避免重复调用
    console.log('ChangeManagement - 分页大小变化(在page-change中)，跳过处理');
  }
};

// 行选择处理
const rowSelectHandler = (selectedRows: ChangeData[]) => {
  state.selectedRows = selectedRows;
};

// 排序处理
const sortChangeHandler = (sortInfo: any) => {
  state.remoteSort = sortInfo;
  loadTableData();
};

// 刷新表格数据
const refreshTableData = () => {
  loadTableData();
};

// 树节点点击处理（完全复制DocumentManage的实现）
const handleTreeNodeClick = (nodeData: any) => {
  console.log("选中项目:", nodeData);

  // 更新选中的项目ID（参考DocumentManage的selectedStageId）
  selectedProjectId.value = nodeData.id;

  // 重置分页到第一页
  state.tablePage.currentPage = 1;

  // 根据选中的项目过滤变更数据
  loadTableData();
};

// 树重置处理（修复为使用正确的根节点ID）
const handleTreeReset = () => {
  // 重置树选择到根节点
  if (treeData.value.length > 0) {
    const rootNodeId = treeData.value[0]?.id || 'root';
    selectedProjectId.value = rootNodeId;

    // 设置树的当前选中节点
    if (treeRef.value) {
      treeRef.value.setCurrentKey(rootNodeId);
    }

    console.log('重置树选择到根节点:', rootNodeId);
  }

  state.tablePage.currentPage = 1;

  // 重新加载数据
  loadTableData();
  // $message.success("已重置到全部项目");
};

// 变更流转配置
const handleFlowConfig = () => {
  showFlowConfigDialog.value = true;
};

// 变更流转配置保存回调
const handleFlowConfigSave = (configData: any) => {
  console.log('变更流转配置保存:', configData);
  $message.success('变更流转配置保存成功');
};

// 新建变更
const handleAddChange = () => {
  showAddDialog.value = true;
  // 重置表单
  addForm.projectName = '';
  addForm.projectCode = '';
};

// 操作处理 - 跳转到项目详情页面并回填数据
const handleOperation = (row: ChangeData) => {
  console.log("查看变更详情:", row);

  // 构建完整的数据对象，确保所有字段都能正确回填
  const passedData = {
    // 基本项目信息
    projectId: row.changeOrderCode || row.projectCode,
    projectCode: row.projectCode || row.changeOrderCode,
    projectName: row.projectName,
    changeId: row.id,

    // 变更表单需要的字段 - 与ProjectDetail组件的changeForm结构对应
    workOrderCode: row.workOrderCode || '',
    workOrderTitle: row.workOrderTitle || `${row.projectName} - 变更申请`,
    creator: row.creator || '',
    createTime: row.createTime || '',
    changeOrderCode: row.changeOrderCode || '',
    specialty: row.specialty || '',
    contractCode: row.contractCode || '',
    contractName: row.contractName || '',
    adjustAmount: row.adjustAmount || '', // 如果有调整金额字段
    description: row.description || '', // 如果有描述字段

    // 额外的状态信息
    currentStage: row.currentStage || '',
    status: row.status || '',

    // 标识这是从变更管理页面跳转过来的
    fromChangeManagement: true,
    isViewMode: true // 标识为查看模式，可能需要禁用某些编辑功能
  };

  console.log("准备传递的数据:", passedData);

  // 跳转到项目详情页面
  emit('jump-to', 'projectDetail', passedData);
};

// 取消新建
const handleCancelAdd = () => {
  showAddDialog.value = false;
};

// 确认新建
const handleConfirmAdd = async () => {
  try {
    await addFormRef.value?.validate();

    // 模拟创建变更
    console.log("创建变更:", addForm);
    $message.success("变更创建成功");

    showAddDialog.value = false;

    // 跳转到项目详情页面
    emit('jump-to', 'projectDetail', {
      projectId: addForm.projectCode,
      projectCode: addForm.projectCode,
      projectName: addForm.projectName,
      isNewChange: true
    });
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

// 拖拽调整面板宽度
const handleResizeStart = (e: MouseEvent) => {
  e.preventDefault();

  const startX = e.clientX;
  const startWidth = leftWidth.value;

  const handleMouseMove = (e: MouseEvent) => {
    const deltaX = e.clientX - startX;
    const newWidth = startWidth + deltaX;

    // 限制最小和最大宽度 - 增加最小宽度确保数量显示
    if (newWidth >= 240 && newWidth <= 500) {
      leftWidth.value = newWidth;
    }
  };

  const handleMouseUp = () => {
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
    document.body.style.cursor = "";
    document.body.style.userSelect = "";
  };

  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
  document.body.style.cursor = "col-resize";
  document.body.style.userSelect = "none";
};

// 加载查询条件
const loadQueryConditions = async () => {
  try {
    const conditions = await mockQueryConditions();
    state.columnSearchOptions = conditions;
  } catch (error) {
    console.error("加载查询条件失败:", error);
  }
};

// 加载项目树数据
const loadTreeData = async () => {
  try {
    console.log('开始加载项目树数据...');
    const treeDataResult = await getProjectTreeData();
    treeData.value = treeDataResult;
    console.log('项目树数据加载完成:', treeDataResult);
  } catch (error) {
    console.error('加载项目树数据失败:', error);
    $message.error('加载项目树数据失败');
    // 设置空数据，避免页面报错
    treeData.value = [];
  }
};

// 加载表格数据
const loadTableData = async () => {
  try {
    state.tableLoading = true;

    // 构建查询条件，包含搜索条件和树选择的联动条件
    const conditions = [];

    // 格式化搜索条件 - 确保包含完整的字段格式
    if (state.conditions && state.conditions.length > 0) {
      state.conditions.forEach((condition: any) => {
        if (condition.value && condition.value.trim()) {
          conditions.push({
            value: condition.value,
            field: condition.field,
            fuzzyable: true,
            operator: "fuzzy"
          });
        }
      });
    }

    // 如果选中了具体的项目（不是根节点），添加项目编号条件
    if (selectedProjectId.value && selectedProjectId.value !== 'root') {
      conditions.push({
        value: selectedProjectId.value,
        field: "projectCode", // 根据接口文档，使用projectCode字段进行联动
        fuzzyable: true,
        operator: "fuzzy"
      });
    }

    const params: ChangeQueryParams = {
      conditions: conditions,
      pageNum: state.tablePage.currentPage,
      pageSize: state.tablePage.pageSize
    };

    console.log('加载变更数据，参数:', params);

    const response = await getChangeListData(params);

    if (response.success) {
      state.tableData = response.rows || [];
      state.tablePage.total = response.total || 0;
      console.log('变更数据加载完成:', response);
    }
  } catch (error) {
    console.error("加载变更数据失败:", error);
    $message.error("加载数据失败");
  } finally {
    state.tableLoading = false;
  }
};

// 精确计算表格高度
const calculatePreciseTableHeight = () => {
  try {
    // 获取页面容器
    const pageContainer = pageContainerRef.value;
    if (!pageContainer) {
      return 400; // 默认高度
    }

    // 基于视口高度计算（参考项目管理页面的成功实现）
    const viewportHeight = window.innerHeight;

    // 计算固定高度元素（参考项目管理页面，但调整为变更管理的布局）
    // 1. 页面padding: 16px * 2 = 32px
    // 2. 页面标题: 18px + 16px(margin) = 34px
    // 3. 搜索区域: searchFormHeight + 16px*2(padding) + 16px(margin) = searchFormHeight + 48px
    // 4. 表格容器padding: 16px * 2 = 32px
    // 5. 表格工具栏: 48px
    // 6. 表格头部: 40px
    // 7. 分页区域: 56px
    // 8. 预留空间: 40px (更保守的预留空间)

    const fixedHeights = {
      pagePadding: 32,                           // 页面内边距
      pageHeader: 34,                            // 页面标题
      searchSection: state.searchFormHeight + 48, // 搜索表单
      tablePadding: 32,                          // 表格容器内边距
      tableToolbar: 48,                          // 表格工具栏
      tableHeader: 40,                           // 表格头部
      pagination: 56,                            // 分页
      reserved: 40                               // 预留空间
    };

    const totalFixedHeight = Object.values(fixedHeights).reduce((sum, height) => sum + height, 0);
    const calculatedHeight = viewportHeight - totalFixedHeight;

    // 确保最小高度为250px
    const finalHeight = Math.max(calculatedHeight, 250);

    // 调试信息
    console.log('表格高度计算详情:', {
      viewportHeight,
      fixedHeights,
      totalFixedHeight,
      calculatedHeight,
      finalHeight,
      searchFormHeight: state.searchFormHeight
    });

    return finalHeight;
  } catch (error) {
    console.error('计算表格高度时出错:', error);
    return 400; // 出错时返回默认高度
  }
};

// 防抖的窗口大小变化处理
const debouncedHandleResize = debounce(() => {
  console.log('窗口大小变化，重新计算表格高度');
  state.tableHeight = calculatePreciseTableHeight();
}, 200);

// 窗口大小变化处理
const handleResize = () => {
  debouncedHandleResize();
};

// 强制重新计算表格高度（用于外部调用）
const forceRecalculateHeight = () => {
  const newHeight = calculatePreciseTableHeight();
  state.tableHeight = newHeight;
  console.log('强制重新计算表格高度:', newHeight);
  return newHeight;
};

// 监听搜索表单的展开/收起状态变化
const handleSearchFormToggle = () => {
  // 延迟一点时间等待动画完成
  setTimeout(() => {
    forceRecalculateHeight();
  }, 300);
};

// 挂载初始化
onMounted(async () => {
  try {
    // 先加载数据
    await loadQueryConditions();

    // 加载项目树数据
    await loadTreeData();

    // 等待DOM完全渲染后计算初始高度
    await nextTick();

    // 设置初始表格高度
    state.tableHeight = calculatePreciseTableHeight();
    console.log('初始表格高度设置完成:', state.tableHeight);

    // 设置默认选中的树节点（参考DocumentManage的实现）
    setTimeout(() => {
      if (treeRef.value && treeData.value.length > 0) {
        // 使用第一个节点的ID作为默认选中项
        const firstNodeId = treeData.value[0]?.id || 'root';
        treeRef.value.setCurrentKey(firstNodeId);
        selectedProjectId.value = firstNodeId;
        console.log('设置默认选中树节点:', firstNodeId);
      }
    }, 100);

    // 加载表格数据
    await loadTableData();

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);

    console.log('变更管理页面初始化完成');
  } catch (error) {
    console.error('变更管理页面初始化失败:', error);
    // 出错时设置默认高度
    state.tableHeight = 400;
  }
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

// 组件选项
defineOptions({
  name: 'ChangeManagementMain'
});
</script>

<style lang="scss" scoped>
.change-management-main {
  height: 100%;
  background: rgb(19, 24, 41);
  padding: 16px;
  display: flex;
  flex-direction: column;

  .page-header {
    margin-bottom: 16px;

    .page-title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;

      .title-icon {
        width: 4px;
        height: 18px;
        background: #409eff;
        margin-right: 12px;
        border-radius: 2px;
      }
    }
  }

  .search-section {
    margin-bottom: 16px;

    .search-form {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      padding: 16px;
    }
  }

  .content-layout {
    flex: 1;
    display: flex;
    gap: 4px;
    min-height: 0;

    .tree-section {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 6px;
      padding: 16px;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .tree-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .tree-title {
          color: var(--el-color-primary);
          font-size: 14px;
          font-weight: 400;
          padding-left: 8px;
          border-left: 3px solid var(--el-color-primary);
        }

        .el-button {
          font-size: 12px;
          padding: 4px 8px;
          height: auto;
          min-height: auto;
        }
      }

      .tree-container {
        height: calc(100% - 50px);
        overflow-y: auto;

        :deep(.el-tree) {
          background: transparent;

          .el-tree-node {
            .el-tree-node__content {
              color: rgba(255, 255, 255, 0.85);
              background: transparent;
              padding: 8px 4px;
              border-radius: 4px;

              &:hover {
                background-color: rgba(255, 255, 255, 0.08);

                .tree-node-count {
                  background: rgba(255, 255, 255, 0.15);
                }
              }

              .tree-node-content {
                display: flex;
                align-items: center;
                flex: 1;
                min-width: 0;
                gap: 4px;

                .tree-node-label {
                  flex: 1;
                  font-size: 13px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  min-width: 0;
                }

                .tree-node-count {
                  flex-shrink: 0;
                  font-size: 12px;
                  color: rgba(255, 255, 255, 0.6);
                  background: rgba(255, 255, 255, 0.1);
                  padding: 2px 6px;
                  border-radius: 10px;
                  min-width: fit-content;
                  text-align: center;
                }
              }
            }

            &.is-current > .el-tree-node__content {
              background-color: rgba(64, 158, 255, 0.2);
              color: var(--el-color-primary);

              .tree-node-content {
                .tree-node-label {
                  color: var(--el-color-primary);
                  font-weight: 500;
                }

                .tree-node-count {
                  background: rgba(64, 158, 255, 0.4);
                  color: #ffffff;
                }
              }
            }

            .el-tree-node__expand-icon {
              color: rgba(255, 255, 255, 0.65);

              &:hover {
                color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }

    .resize-handle {
      width: 4px;
      background: rgba(255, 255, 255, 0.1);
      cursor: col-resize;
      border-radius: 2px;
      transition: background-color 0.2s;

      &:hover {
        background: rgba(64, 158, 255, 0.5);
      }
    }

    .right-content {
      flex: 1;
      min-width: 0;

      .table-section {
        height: 100%;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 16px;

        :deep(.im-table) {
          .el-table {
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.1);

            .el-table__header {
              background: rgba(255, 255, 255, 0.05);

              th {
                background: transparent;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                color: #ffffff;
              }
            }


          }

          .el-pagination {
            background: transparent;
            color: #ffffff;

            .el-pager li {
              background: transparent;
              color: #ffffff;
              border: 1px solid rgba(255, 255, 255, 0.1);

              &.is-active {
                background: #409eff;
                border-color: #409eff;
              }
            }

            .btn-prev,
            .btn-next {
              background: transparent;
              color: #ffffff;
              border: 1px solid rgba(255, 255, 255, 0.1);
            }
          }
        }
      }
    }
  }
}

// 弹窗样式
:deep(.el-dialog) {
  background: rgb(19, 24, 41);
  border: 1px solid rgba(255, 255, 255, 0.1);

  .el-dialog__header {
    // background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .el-dialog__title {
      color: #ffffff;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #ffffff;
      }
    }
  }

  .el-dialog__body {
    background: rgb(19, 24, 41);
    color: #ffffff;
    padding: 10px 0;
    .el-form {
      .el-form-item__label {
        color: #ffffff;
      }

      .el-input {
        .el-input__wrapper {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);

          .el-input__inner {
            background: transparent;
            color: #ffffff;

            &::placeholder {
              color: rgba(255, 255, 255, 0.5);
            }
          }
        }
      }

      .el-select {
        .el-select__wrapper {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);

          .el-select__placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          .el-select__selected-item {
            color: #ffffff;
          }

          .el-select__caret {
            color: #ffffff;
          }
        }
      }
    }
  }

  .el-dialog__footer {
    // background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}
</style>
