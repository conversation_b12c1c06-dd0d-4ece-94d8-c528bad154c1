<template>
  <div class="project-detail-container">
    <!-- 头部区域 -->
    <div class="detail-header">
      <div class="header-left">
        <el-icon class="header-icon" color="#409EFF">
          <Document />
        </el-icon>
        <span class="header-title">提交申请</span>
      </div>
      <div class="header-right">
        <el-button size="default" type="primary" @click="handleSubmit">
          提交
        </el-button>
        <el-button size="default" @click="handleBack">
          返回
        </el-button>
      </div>
    </div>

    <!-- 项目标题 -->
    <div class="project-title">
      <el-icon class="title-icon" color="#409EFF">
        <Document />
      </el-icon>
      <span class="title-text">{{ projectData.projectName || '项目名称未加载' }}</span>
    </div>





    <!-- 标签页 -->
    <div class="tabs-container">
      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="变更信息" name="changeInfo">
          <!-- 变更信息表单 -->
          <div class="change-info-section">
            <div class="section-header">
              <div class="section-title">变更信息</div>
            </div>

            <el-form :model="changeForm" :rules="formRules" ref="changeFormRef" label-width="120px" class="change-form">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="工单编号：" prop="workOrderCode" required>
                    <el-input
                      v-model="changeForm.workOrderCode"
                      placeholder="请输入工单编号"
                      class="form-input"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="工单标题：" prop="workOrderTitle" required>
                    <el-input
                      v-model="changeForm.workOrderTitle"
                      placeholder="请输入工单标题"
                      class="form-input"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="创建人：" prop="creator" required>
                    <el-input
                      v-model="changeForm.creator"
                      placeholder="请输入创建人"
                      class="form-input"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="创建时间：" prop="createTime" required>
                    <el-date-picker
                      v-model="changeForm.createTime"
                      type="datetime"
                      placeholder="请选择创建时间"
                      class="form-input"
                      format="YYYY-MM-DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="变更单编号：" prop="changeOrderCode" required>
                    <el-input
                      v-model="changeForm.changeOrderCode"
                      placeholder="请输入变更单编号"
                      class="form-input"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="项目名称：" prop="projectName" required>
                    <el-input
                      v-model="changeForm.projectName"
                      placeholder="请输入项目名称"
                      class="form-input"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="专业：" prop="specialty" required>
                    <el-input
                      v-model="changeForm.specialty"
                      placeholder="请输入专业"
                      class="form-input"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同编号：" prop="contractCode" required>
                    <el-input
                      v-model="changeForm.contractCode"
                      placeholder="请输入合同编号"
                      class="form-input"
                      @blur="handleContractCodeChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="合同名称：" prop="contractName" required>
                    <el-input
                      v-model="changeForm.contractName"
                      placeholder="请输入合同名称"
                      class="form-input"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="调整合同金额：" prop="adjustAmount" required>
                    <el-input
                      v-model="changeForm.adjustAmount"
                      placeholder="请输入调整后的合同金额"
                      class="form-input"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="变更内容说明：" prop="description" required>
                    <el-input
                      v-model="changeForm.description"
                      type="textarea"
                      :rows="4"
                      placeholder="请详细描述变更内容和原因"
                      class="form-textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 变更附件 -->
          <div class="attachments-section">
            <div class="section-header">
              <div class="section-title">变更附件</div>
            </div>

            <div class="attachments-table">
              <el-table :data="attachments" style="width: 100%" class="custom-table" height="400">
                <el-table-column prop="index" label="#" width="80" align="center" />
                <el-table-column prop="description" label="附件信息" min-width="200" />
                <el-table-column prop="fileName" label="附件名称" min-width="300">
                  <template #default="{ row }">
                    <el-link type="primary" @click="handleFilePreview(row)">
                      {{ row.fileName }}
                    </el-link>
                  </template>
                </el-table-column>
                <el-table-column prop="uploadStatus" label="是否上传" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag :type="row.uploadStatus === '已上传' ? 'success' : 'warning'">
                      {{ row.uploadStatus }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template #default="{ row }">
                    <el-link type="primary" @click="handleFileAction(row)">
                      {{ row.action }}
                    </el-link>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="审批记录" name="approvalRecord">
          <!-- 审批记录内容 -->
          <div class="approval-section">
            <!-- 流程图区域 -->
            <div class="process-flow-section">
              <div class="flow-chart-container">
                <!-- 流程图占位区域 -->
                <div class="flow-chart-placeholder">
                  <div class="flow-chart-fallback">
                    <el-icon size="48" color="#409EFF">
                      <Document />
                    </el-icon>
                    <p>流程图占位区域</p>
                    <p class="placeholder-desc">此处将显示审批流程图</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 流转记录表格 -->
            <div class="approval-records-section">
              <div class="section-header">
                <div class="section-title">流转记录</div>
              </div>

              <div class="records-table">
                <el-table :data="approvalFlowRecords" style="width: 100%" class="flow-records-table" height="400">
                  <el-table-column prop="stage" label="所属阶段" width="120" align="center" />
                  <el-table-column prop="processor" label="处理人" width="200" align="center" />
                  <el-table-column prop="department" label="处理部门" min-width="200" />
                  <el-table-column prop="arrivalTime" label="到达时间" width="150" align="center" />
                  <el-table-column prop="completionTime" label="完成时间" width="150" align="center" />
                  <el-table-column prop="processingTime" label="处理时长" width="100" align="center" />
                  <el-table-column prop="result" label="处理意见" width="100" align="center">
                    <template #default="{ row }">
                      <span v-if="row.result">{{ row.result }}</span>
                      <span v-else class="no-result">-</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 提交变更弹窗 -->
    <el-dialog
      v-model="submitDialogVisible"
      title="提交变更"
      width="600px"
      :before-close="handleDialogClose"
      class="submit-dialog"
    >
      <div class="submit-form-container">
        <el-form :model="submitForm" label-width="120px" class="submit-form">
          <el-form-item label="当前处理人">
            <el-input v-model="submitForm.currentProcessor" readonly class="readonly-input" />
          </el-form-item>

          <el-form-item label="当前处理时间">
            <el-input v-model="submitForm.currentTime" readonly class="readonly-input" />
          </el-form-item>

          <el-form-item label="操作选项" >
            <el-radio-group v-model="submitForm.operation" >
              <el-radio label="变更审批"></el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="主送人">
            <el-select
              v-model="submitForm.recipient"
              placeholder="请选择"
              class="recipient-select"
              style="width: 100%"
            >
              <el-option label="请选择" value="" />
              <el-option label="张主管" value="zhang" />
              <el-option label="李经理" value="li" />
              <el-option label="王总监" value="wang" />
            </el-select>
          </el-form-item>

          <el-form-item label="处理意见">
            <el-input
              v-model="submitForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入处理意见"
              class="comment-textarea"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose" class="cancel-btn">取消</el-button>
          <el-button type="primary" @click="handleSubmitConfirm" class="confirm-btn">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="ProjectDetail">
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Document } from '@element-plus/icons-vue';
import { getProjectDetail } from './api';

// Props
interface Props {
  projectId?: string;
  needsPassedData?: any;
}

const props = defineProps<Props>();



// Emits
const emit = defineEmits<{
  'back': [];
  'submit': [data: any];
}>();

// 响应式数据
const activeTab = ref('changeInfo');
const changeFormRef = ref();
const projectData = ref({
  projectName: '',
  projectCode: '',
  status: ''
});

const changeForm = ref({
  workOrderCode: '',
  workOrderTitle: '',
  creator: '',
  createTime: '',
  changeOrderCode: '',
  projectName: '',
  specialty: '',
  contractCode: '',
  contractName: '',
  adjustAmount: '',
  description: ''
});

// 表单验证规则
const formRules = {
  workOrderCode: [
    { required: true, message: '请输入工单编号', trigger: 'blur' }
  ],
  workOrderTitle: [
    { required: true, message: '请输入工单标题', trigger: 'blur' }
  ],
  creator: [
    { required: true, message: '请输入创建人', trigger: 'blur' }
  ],
  createTime: [
    { required: true, message: '请选择创建时间', trigger: 'change' }
  ],
  changeOrderCode: [
    { required: true, message: '请输入变更单编号', trigger: 'blur' }
  ],
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  specialty: [
    { required: true, message: '请选择专业', trigger: 'change' }
  ],
  contractCode: [
    { required: true, message: '请选择合同编号', trigger: 'change' }
  ],
  contractName: [
    { required: true, message: '请输入合同名称', trigger: 'blur' }
  ],
  adjustAmount: [
    { required: true, message: '请输入调整合同金额', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入变更内容说明', trigger: 'blur' }
  ]
};

// 合同选项数据
const contractOptions = ref([
  {
    code: 'HT2024001',
    name: 'DC2机房配套设备采购合同',
    amount: '5000000'
  },
  {
    code: 'HT2024002',
    name: '网络设备升级改造合同',
    amount: '3200000'
  },
  {
    code: 'HT2024003',
    name: '5G基站建设施工合同',
    amount: '8500000'
  },
  {
    code: 'HT2024004',
    name: '光纤网络扩容工程合同',
    amount: '2800000'
  },
  {
    code: 'HT2024005',
    name: '通信基础设施建设合同',
    amount: '6700000'
  }
]);

// 提交弹窗相关数据
const submitDialogVisible = ref(false);
const submitForm = ref({
  currentProcessor: '张老师',
  currentTime: '',
  operation: 'approve',
  recipient: '',
  comment: ''
});

const attachments = ref([
  {
    index: 1,
    description: '变更单',
    fileName: '机电工程变更说明.pdf',
    uploadStatus: '已上传',
    action: '查看'
  },
  {
    index: 2,
    description: '技术方案',
    fileName: '技术变更详细方案.docx',
    uploadStatus: '已上传',
    action: '下载'
  },
  {
    index: 3,
    description: '预算清单',
    fileName: '变更预算明细表.xlsx',
    uploadStatus: '已上传',
    action: '查看'
  },
  {
    index: 4,
    description: '设计图纸',
    fileName: 'DC2机房布局设计图.dwg',
    uploadStatus: '未上传',
    action: '上传'
  },
  {
    index: 5,
    description: '合同附件',
    fileName: '补充协议书.pdf',
    uploadStatus: '已上传',
    action: '下载'
  },
  {
    index: 6,
    description: '验收报告',
    fileName: '设备验收报告.pdf',
    uploadStatus: '未上传',
    action: '上传'
  },
  {
    index: 7,
    description: '安全评估',
    fileName: '安全风险评估报告.pdf',
    uploadStatus: '已上传',
    action: '查看'
  },
  {
    index: 8,
    description: '环境影响',
    fileName: '环境影响评估.docx',
    uploadStatus: '未上传',
    action: '上传'
  }
]);

// 流转记录数据
const approvalFlowRecords = ref([
  {
    stage: '变更申请',
    processor: '孙老师 (17600006699)',
    department: '北京市分公司云网建设中心',
    arrivalTime: '2025-05-30',
    completionTime: '2025-05-30 12:30:30',
    processingTime: '3分52秒',
    result: '-'
  },
  {
    stage: '初步审核',
    processor: '王主管 (13800138001)',
    department: '北京市分公司工程部',
    arrivalTime: '2025-05-30 12:35:00',
    completionTime: '2025-05-30 14:20:15',
    processingTime: '1小时45分',
    result: '通过'
  },
  {
    stage: '技术评审',
    processor: '张工程师 (13900139002)',
    department: '技术中心设计院',
    arrivalTime: '2025-05-30 14:25:00',
    completionTime: '2025-05-31 09:15:30',
    processingTime: '18小时50分',
    result: '通过'
  },
  {
    stage: '变更审核',
    processor: '李老师 (17600006699)',
    department: '北京市分公司云网建设中心',
    arrivalTime: '2025-05-31 09:20:00',
    completionTime: '2025-05-31 11:45:30',
    processingTime: '2小时25分',
    result: '通过'
  },
  {
    stage: '预算审核',
    processor: '陈会计 (15800158003)',
    department: '财务部预算科',
    arrivalTime: '2025-05-31 11:50:00',
    completionTime: '2025-05-31 16:30:45',
    processingTime: '4小时40分',
    result: '通过'
  },
  {
    stage: '安全评估',
    processor: '刘安全员 (18600186004)',
    department: '安全监督部',
    arrivalTime: '2025-05-31 16:35:00',
    completionTime: '2025-06-01 10:20:15',
    processingTime: '17小时45分',
    result: '通过'
  },
  {
    stage: '部门会签',
    processor: '郭老师 (17600006699)',
    department: '北京市分公司云网建设中心',
    arrivalTime: '2025-06-01 10:25:00',
    completionTime: '2025-06-01 15:30:30',
    processingTime: '5小时5分',
    result: '通过'
  },
  {
    stage: '领导审批',
    processor: '赵总监 (13700137005)',
    department: '北京市分公司',
    arrivalTime: '2025-06-01 15:35:00',
    completionTime: '2025-06-02 09:10:20',
    processingTime: '17小时35分',
    result: '通过'
  },
  {
    stage: '最终确认',
    processor: '总经理 (13600136006)',
    department: '北京市分公司',
    arrivalTime: '2025-06-02 09:15:00',
    completionTime: '2025-06-02 11:45:50',
    processingTime: '2小时30分',
    result: '通过'
  },
  {
    stage: '归档',
    processor: '系统',
    department: '-',
    arrivalTime: '2025-06-02 11:50:00',
    completionTime: '2025-06-02 11:50:30',
    processingTime: '30秒',
    result: '-'
  },
  {
    stage: '执行通知',
    processor: '项目经理 (15900159007)',
    department: '项目管理部',
    arrivalTime: '2025-06-02 12:00:00',
    completionTime: '2025-06-02 14:30:15',
    processingTime: '2小时30分',
    result: '已通知'
  },
  {
    stage: '实施跟踪',
    processor: '监理工程师 (18800188008)',
    department: '工程监理部',
    arrivalTime: '2025-06-02 14:35:00',
    completionTime: '进行中',
    processingTime: '进行中',
    result: '跟踪中'
  }
]);



// 方法
const handleBack = () => {
  emit('back');
};

// 处理合同编号变更 - 适配输入框模式
const handleContractCodeChange = () => {
  const contractCode = changeForm.value.contractCode;
  if (contractCode && contractCode.trim()) {
    // 尝试从预设的合同选项中匹配
    const selectedContract = contractOptions.value.find(contract =>
      contract.code === contractCode.trim()
    );
    if (selectedContract) {
      // 如果找到匹配的合同，自动填充合同名称和金额
      changeForm.value.contractName = selectedContract.name;
      if (!changeForm.value.adjustAmount) {
        changeForm.value.adjustAmount = selectedContract.amount;
      }
      console.log('自动匹配合同信息:', selectedContract);
    }
    // 如果没有找到匹配的合同，保持用户输入的合同编号，不清空合同名称
  }
};

const handleSubmit = () => {
  // 验证表单
  if (!changeFormRef.value) return;

  changeFormRef.value.validate((valid: boolean) => {
    if (valid) {
      // 设置当前时间
      const now = new Date();
      submitForm.value.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-');

      // 显示提交弹窗
      submitDialogVisible.value = true;
    } else {
      ElMessage.warning('请完善必填信息');
    }
  });
};

const handleFilePreview = (file: any) => {
  ElMessage.info(`预览文件: ${file.fileName}`);
};

const handleFileAction = (file: any) => {
  ElMessage.info(`${file.action}文件: ${file.fileName}`);
};

// 弹窗处理方法
const handleDialogClose = () => {
  submitDialogVisible.value = false;
  // 重置表单
  submitForm.value.recipient = '';
  submitForm.value.comment = '';
};

const handleSubmitConfirm = () => {
  if (!submitForm.value.recipient) {
    ElMessage.warning('请选择主送人');
    return;
  }

  // 提交数据
  const submitData = {
    ...changeForm.value,
    submitInfo: submitForm.value
  };

  ElMessage.success('提交成功');
  emit('submit', submitData);

  // 关闭弹窗
  handleDialogClose();
};



// 监听 props 变化 - 优化数据回填逻辑
watch(() => props.needsPassedData, (newData) => {
  if (newData && typeof newData === 'object') {
    console.log('ProjectDetail: 接收到传递的数据:', newData);

    // 更新项目数据
    projectData.value = {
      ...projectData.value,
      projectName: newData.projectName || projectData.value.projectName,
      projectCode: newData.projectCode || projectData.value.projectCode,
      status: newData.status || projectData.value.status
    };

    // 完整回填变更表单数据 - 确保所有字段都能正确映射
    const formFields = {
      workOrderCode: newData.workOrderCode || '',
      workOrderTitle: newData.workOrderTitle || (newData.projectName ? `${newData.projectName} - 变更申请` : ''),
      creator: newData.creator || '',
      createTime: newData.createTime || '',
      changeOrderCode: newData.changeOrderCode || '',
      projectName: newData.projectName || '',
      specialty: newData.specialty || '',
      contractCode: newData.contractCode || '',
      contractName: newData.contractName || '',
      adjustAmount: newData.adjustAmount || '',
      description: newData.description || ''
    };

    // 批量更新表单字段，只更新有值的字段
    Object.keys(formFields).forEach(key => {
      if (formFields[key] && formFields[key].trim() !== '') {
        changeForm.value[key] = formFields[key];
      }
    });

    // 如果有合同编号但没有合同名称，尝试从合同选项中匹配
    if (newData.contractCode && !newData.contractName) {
      const matchedContract = contractOptions.value.find(contract =>
        contract.code === newData.contractCode
      );
      if (matchedContract) {
        changeForm.value.contractName = matchedContract.name;
        // 如果没有调整金额，可以使用合同金额作为参考
        if (!changeForm.value.adjustAmount) {
          changeForm.value.adjustAmount = matchedContract.amount;
        }
      }
    }

    // 特殊处理：如果是从变更管理页面跳转过来的，可能需要设置特殊状态
    if (newData.fromChangeManagement) {
      console.log('ProjectDetail: 来自变更管理页面的数据');
      // 可以在这里设置一些特殊的状态或行为
    }

    console.log('ProjectDetail: 表单数据回填完成:', changeForm.value);
    console.log('ProjectDetail: 项目数据更新完成:', projectData.value);
  }
}, { immediate: true, deep: true });

// 初始化数据 - 优化数据加载和回填逻辑
onMounted(async () => {
  console.log('ProjectDetail: 组件初始化，props:', {
    projectId: props.projectId,
    needsPassedData: props.needsPassedData
  });

  // 优先使用传递的数据，如果没有再尝试加载
  if (props.needsPassedData && Object.keys(props.needsPassedData).length > 0) {
    console.log('ProjectDetail: 使用传递的数据，跳过API加载');
    return; // 传递的数据已经在watch中处理了
  }

  // 如果没有传递数据，尝试通过API加载
  if (props.projectId || props.needsPassedData?.projectId) {
    const projectId = props.projectId || props.needsPassedData?.projectId;

    try {
      console.log('ProjectDetail: 通过API加载项目数据，projectId:', projectId);
      const data = await getProjectDetail(projectId);

      // 更新项目基本信息
      projectData.value = {
        ...projectData.value,
        projectName: data.projectName || projectData.value.projectName,
        projectCode: data.projectCode || projectData.value.projectCode,
        status: data.status || projectData.value.status
      };

      console.log('ProjectDetail: API加载的项目数据:', data);

      // 批量填充表单数据 - 只填充有值的字段
      const apiFormFields = {
        workOrderCode: data.workOrderCode,
        workOrderTitle: data.workOrderTitle,
        changeOrderCode: data.changeOrderCode,
        projectName: data.projectName,
        specialty: data.specialty,
        contractCode: data.contractCode,
        contractName: data.contractName,
        adjustAmount: data.adjustAmount,
        description: data.description,
        creator: data.creator,
        createTime: data.createTime
      };

      Object.keys(apiFormFields).forEach(key => {
        if (apiFormFields[key] && apiFormFields[key].trim() !== '') {
          changeForm.value[key] = apiFormFields[key];
        }
      });

      // 合同名称匹配逻辑
      if (data.contractCode && !data.contractName) {
        const matchedContract = contractOptions.value.find(contract =>
          contract.code === data.contractCode
        );
        if (matchedContract) {
          changeForm.value.contractName = matchedContract.name;
          // 如果没有调整金额，使用合同金额
          if (!changeForm.value.adjustAmount) {
            changeForm.value.adjustAmount = matchedContract.amount;
          }
        }
      }

      console.log('ProjectDetail: API数据填充完成:', {
        projectData: projectData.value,
        changeForm: changeForm.value
      });
    } catch (error) {
      console.error('ProjectDetail: 加载项目详情失败:', error);
      ElMessage.error('加载项目详情失败');
    }
  } else {
    console.log('ProjectDetail: 没有提供projectId，使用默认数据');
  }
});
</script>

<style scoped lang="scss">
.project-detail-container {
  background: rgb(19, 24, 41);
  min-height: 100vh;
  padding: 24px;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .header-icon {
        font-size: 20px;
      }

      .header-title {
        color: #fff;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .project-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;

    .title-icon {
      font-size: 16px;
    }

    .title-text {
      color: #fff;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .tabs-container {
    .detail-tabs {
      :deep(.el-tabs__header) {
        margin-bottom: 20px;

        .el-tabs__nav-wrap {
          &::after {
            background-color: rgba(255, 255, 255, 0.1);
          }
        }

        .el-tabs__item {
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;

          &.is-active {
            color: var(--el-color-primary);
          }

          &:hover {
            color: rgba(255, 255, 255, 0.9);
          }
        }

        .el-tabs__active-bar {
          background-color: var(--el-color-primary);
        }
      }

      :deep(.el-tabs__content) {
        .el-tab-pane {
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }

  .change-info-section {
    .section-header {
      margin-bottom: 20px;

      .section-title {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        padding-left: 8px;
        border-left: 3px solid var(--el-color-primary);
      }
    }

    .change-form {
      :deep(.el-form-item__label) {
        color: rgba(255, 255, 255, 0.9);
      }

      :deep(.el-input__wrapper) {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:hover {
          border-color: rgba(255, 255, 255, 0.3);
        }

        &.is-focus {
          border-color: var(--el-color-primary);
        }

        .el-input__inner {
          color: rgba(255, 255, 255, 0.9);

          &::placeholder {
            color: rgba(255, 255, 255, 0.4);
          }
        }
      }

      :deep(.el-textarea__inner) {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.9);

        &::placeholder {
          color: rgba(255, 255, 255, 0.4);
        }

        &:hover {
          border-color: rgba(255, 255, 255, 0.3);
        }

        &:focus {
          border-color: var(--el-color-primary);
        }
      }

      :deep(.el-select) {
        .el-input__wrapper {
          background-color: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
      }
    }
  }

  .approval-section {
    .process-flow-section {
      margin-bottom: 32px;

      .flow-chart-container {
        background: rgba(255, 255, 255, 0.02);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 20px;
        text-align: center;

        .flow-chart-placeholder {
          position: relative;
          min-height: 300px;
          display: flex;
          align-items: center;
          justify-content: center;

          .flow-chart-image {
            max-width: 100%;
            max-height: 400px;
            object-fit: contain;
          }

          .flow-chart-fallback {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            color: rgba(255, 255, 255, 0.6);

            p {
              margin: 0;
              font-size: 16px;

              &.placeholder-desc {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.4);
              }
            }
          }
        }
      }
    }

    .approval-records-section {
      .section-header {
        margin-bottom: 20px;

        .section-title {
          color: #fff;
          font-size: 16px;
          font-weight: 600;
          padding-left: 8px;
          border-left: 3px solid var(--el-color-primary);
        }
      }

      .records-table {
        :deep(.flow-records-table) {
          .el-table__header {
            background-color: rgba(255, 255, 255, 0.05);

            th {
              background-color: transparent;
              color: #fff;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);
              height: 48px;
              font-weight: 600;
            }
          }

          .el-table__body {
            tr {
              background-color: transparent;

              &:hover {
                background-color: rgba(255, 255, 255, 0.05) !important;
              }

              td {
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
                color: rgba(255, 255, 255, 0.9);
                padding: 12px 0;

                .no-result {
                  color: rgba(255, 255, 255, 0.4);
                }
              }
            }
          }

          .el-table__border-left-patch,
          .el-table__border-right-patch {
            background-color: rgba(255, 255, 255, 0.1);
          }

          &::before {
            background-color: rgba(255, 255, 255, 0.1);
          }

          .el-table__border {
            border-color: rgba(255, 255, 255, 0.1);
          }
        }
      }
    }
  }

  .attachments-section {
    margin-top: 40px;

    .section-header {
      margin-bottom: 20px;

      .section-title {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        padding-left: 8px;
        border-left: 3px solid var(--el-color-primary);
      }
    }

    .attachments-table {
      :deep(.custom-table) {
        .el-table__header {
          background-color: rgba(255, 255, 255, 0.05);

          th {
            background-color: transparent;
            color: #fff;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            height: 48px;
          }
        }

        .el-table__body {
          tr {
            background-color: transparent;

            &:hover {
              background-color: rgba(255, 255, 255, 0.05) !important;
            }

            td {
              border-bottom: 1px solid rgba(255, 255, 255, 0.05);
              color: rgba(255, 255, 255, 0.9);
              padding: 12px 0;
            }
          }
        }

        .el-table__border-left-patch,
        .el-table__border-right-patch {
          background-color: rgba(255, 255, 255, 0.1);
        }

        &::before {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .el-table__border {
          border-color: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  // 提交弹窗样式
  :deep(.submit-dialog) {
    .el-dialog__header {
      // background-color: rgba(255, 255, 255, 0.05);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .el-dialog__title {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
      }

      .el-dialog__headerbtn {
        .el-dialog__close {
          color: rgba(255, 255, 255, 0.7);

          &:hover {
            color: #fff;
          }
        }
      }
    }

    .el-dialog__body {
      // background-color: rgba(255, 255, 255, 0.02);
      padding: 20px;

      .submit-form-container {
        .submit-form {
          :deep(.el-form-item__label) {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
          }

          :deep(.operation-form-item) {
            .el-form-item__content {
              display: flex;
              align-items: center;
            }
          }

          .readonly-input {
            :deep(.el-input__wrapper) {
              // background-color: rgba(255, 255, 255, 0.03);
              border: 1px solid rgba(255, 255, 255, 0.1);

              .el-input__inner {
                color: rgba(255, 255, 255, 0.7);
                cursor: not-allowed;
              }
            }
          }

          .operation-radio {
            display: flex;
            align-items: center;
            height: 32px; // 确保与标签高度一致

            :deep(.el-radio) {
              display: flex;
              align-items: center;
              margin: 0;

              .el-radio__input {
                display: flex;
                align-items: center;
              }

              .el-radio__label {
                color: rgba(255, 255, 255, 0.9);
                display: flex;
                align-items: center;

                .radio-text {
                  font-size: 14px;
                  line-height: 1;
                }
              }

              .el-radio__input.is-checked {
                .el-radio__inner {
                  background-color: var(--el-color-primary);
                  border-color: var(--el-color-primary);
                }
              }

              .el-radio__inner {
                border-color: rgba(255, 255, 255, 0.3);

                &:hover {
                  border-color: var(--el-color-primary);
                }
              }
            }
          }

          .recipient-select {
            :deep(.el-input__wrapper) {
              background-color: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.2);

              &:hover {
                border-color: rgba(255, 255, 255, 0.3);
              }

              &.is-focus {
                border-color: var(--el-color-primary);
              }

              .el-input__inner {
                color: rgba(255, 255, 255, 0.9);

                &::placeholder {
                  color: rgba(255, 255, 255, 0.4);
                }
              }

              .el-input__suffix {
                .el-input__suffix-inner {
                  .el-select__caret {
                    color: rgba(255, 255, 255, 0.6);
                  }
                }
              }
            }
          }

          .comment-textarea {
            :deep(.el-textarea__inner) {
              background-color: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.2);
              color: rgba(255, 255, 255, 0.9);

              &::placeholder {
                color: rgba(255, 255, 255, 0.4);
              }

              &:hover {
                border-color: rgba(255, 255, 255, 0.3);
              }

              &:focus {
                border-color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }

    .el-dialog__footer {
      // background-color: rgba(255, 255, 255, 0.05);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      padding: 15px 20px;

      .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        .cancel-btn {
          background-color: transparent;
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: rgba(255, 255, 255, 0.9);

          &:hover {
            border-color: rgba(255, 255, 255, 0.5);
            background-color: rgba(255, 255, 255, 0.05);
          }
        }

        .confirm-btn {
          background-color: var(--el-color-primary);
          border-color: var(--el-color-primary);

          &:hover {
            background-color: var(--el-color-primary-light-3);
            border-color: var(--el-color-primary-light-3);
          }
        }
      }
    }
  }
}

// 全局弹窗样式覆盖
:deep(.el-dialog) {
  background-color: rgb(19, 24, 41);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-select-dropdown) {
  background-color: rgb(19, 24, 41);
  border: 1px solid rgba(255, 255, 255, 0.1);

  .el-select-dropdown__item {
    color: rgba(255, 255, 255, 0.9);

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    &.selected {
      background-color: var(--el-color-primary);
      color: #fff;
    }
  }
}
</style>
