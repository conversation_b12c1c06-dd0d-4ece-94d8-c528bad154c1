import { http } from "@/utils/http";

// 项目详情视图数据类型
export interface ProjectDetailViewData {
  projectName: string;
  projectCode: string;
  approvalNumber: string;
  location: string;
  constructionMethod: string;
  constructionNature: string;
  fundingSource: string;
  currentStage: string;
  availableBudget: string;
  constructionUnit: string;
  basicConstructionCode: string;
  constructionUnitMethod: string;
  projectType: string;
  isGeneralContract: string;
  projectManager: string;
  projectManagerMethod: string;
  constructionContent: string;
  constructionDetails: string;
  designBudget: string;
  currentBudget: string;
  newBudget: string;
  totalBudget: string;
}

// 接口返回的项目详情数据类型 - 根据最新接口字段更新
export interface ApiProjectDetailData {
  // 基本信息
  prov?: string; // 省分
  city?: string; // 地市
  projectId?: string; // 项目ID
  projectCode?: string; // 项目编码
  projectName?: string; // 项目名称
  disYear?: string; // 建设年份
  touziSubject?: string; // 投资主体
  specA?: string; // A级专业
  specB?: string; // B级专业
  specC?: string; // C级专业
  legalSubject?: string; // 法人主体
  networkLevel?: string; // 网络层面
  bulidScene?: string; // 建设性质
  remandUnit?: string; // 需求单位
  disDept?: string; // 标准建设部门
  projectPolicy?: string; // 实际部门
  projectTrial?: string; // 项目审批方式
  isPurchase?: string; // 建设方式
  manageType?: string; // 项目管理类别
  postDisaster?: string; // 项目专项标识
  managerName?: string; // 项目经理姓名
  managerEmail?: string; // 项目经理邮箱前缀

  // 批复信息
  lxDate?: string; // 立项批复时间
  lxSn?: string; // 立项批复文号
  lxTouZi?: string; // 立项批复总投资（元）
  lxCapex?: string; // 立项批复本年CAPEX（元）
  kySn?: string; // 可研批复文号
  kyTouZi?: string; // 可研批复总投资（元）
  kyCapex?: string; // 可研批复本年CAPEX（元）
  sjSn?: string; // 设计批复文号
  sjTouZi?: string; // 设计批复总投资（元）
  sjCapex?: string; // 设计批复本年CAPEX（元）
  kyDate?: string; // 可研批复时间
  sjDate?: string; // 设计批复时间

  // 项目状态
  proState?: string; // 项目状态
  isValid?: string; // 是否有效

  // 关联项目信息
  ytProjectId?: string; // 待立项项目ID
  dkyProjectCode?: string; // 关联可研项目编号
  dkyProjectName?: string; // 关联可研项目名称
  dkyTouZi?: string; // 关联可研项目立项批复总投资（元）

  // 验收信息
  firstAccDate?: string; // 初验时间
  firstAccTouzi?: string; // 初验验收总投资（元）
  finalAccDate?: string; // 终验时间
  finalAccTouzi?: string; // 终验验收总投资（元）

  // 建设规模信息
  lxBulidContent?: string; // 立项建设规模信息
  lxtzTouZi?: string; // 立项调整投资（元）
  lxtzDate?: string; // 立项调整批复时间
  lxtzBulidContent?: string; // 立项调整建设规模信息
  sjBulidContent?: string; // 设计建设规模信息
  sjtzTouZi?: string; // 设计调整投资（元）
  sjtzDate?: string; // 设计调整批复时间
  sjtzBulidContent?: string; // 设计调整建设规模信息
  mainbuildContent?: string; // 主要建设内容
  buildScene?: string; // 建设性质
  projectState?: string; // 项目当前阶段

  // 计划时间
  planStartDate?: string; // 计划开工时间
  planEndDate?: string; // 计划完工时间
  planFirstYsDate?: string; // 计划初验时间
  planLastYsDate?: string; // 计划终验时间
  planYsEndDate?: string; // 计划验收完成时间

  // 实际时间
  startkyDate?: string; // 发起可研审批时间
  startSjDate?: string; // 发起设计审批时间
  actualStartDate?: string; // 实际开工时间
  actualEndDate?: string; // 实际完工时间
  actualFirstYsDate?: string; // 实际初验时间
  actualLastYsDate?: string; // 实际终验时间
  actualYsEndDate?: string; // 实际验收完成时间

  // 系统字段
  createdBy?: string; // 创建人
  createdTime?: string; // 创建时间
  updatedBy?: string; // 更新人
  updatedTime?: string; // 更新时间
  isShow?: string; // 是否展示到项目列表
  isHand?: string; // 是否手动添加
  label?: string; // 标签
  isDelivery?: string; // 是否交付
}

// API响应格式
export interface ApiResponse<T> {
  status: string;
  msg: string;
  data: T;
}

// 项目阶段数据类型
export interface ProjectStageData {
  stage: string;
  status: 'overdue' | 'normal' | 'pending';
  date: string;
  description: string;
}

// 新的项目阶段数据类型 - 对应真实接口
export interface ProjectPhaseData {
  phaseKey: string;
  phaseName: string;
  date: string | null;
  status: 'overdue' | 'normal';
}

// 项目阶段响应数据类型
export interface ProjectPhasesResponse {
  activeTab: 'plan' | 'actual';
  planPhases: ProjectPhaseData[];
  actualPhases: ProjectPhaseData[];
}

// 设计信息数据类型
export interface DesignInfoData {
  designApprovalNumber: string;
  designApprovalAmount: string;
  designApprovalDate: string;
  designUnit: string;
  designPersonInCharge: string;
  designPersonMethod: string;
  designDate: string;
  designAmount: string;
}



/**
 * 获取项目阶段数据 - 对接真实接口
 * @param projectCode 项目编码
 * @returns Promise<ProjectPhasesResponse>
 */
export const getProjectPhases = async (projectCode: string): Promise<ProjectPhasesResponse> => {
  try {
    const response = await http.request<ApiResponse<ProjectPhasesResponse>>(
      "get",
      `/project/getProjectPhases?projectCode=${projectCode}`
    );

    if (response.status === '0') {
      return response.data;
    } else {
      throw new Error(response.msg || '获取项目阶段数据失败');
    }
  } catch (error) {
    console.error('获取项目阶段数据失败:', error);
    // 返回默认数据，避免页面崩溃
    return {
      activeTab: 'plan',
      planPhases: [],
      actualPhases: []
    };
  }
};

/**
 * 将接口返回的阶段数据转换为组件需要的格式
 * @param phases 接口返回的阶段数据
 * @returns ProjectStageData[]
 */
export const convertPhasesToStageData = (phases: ProjectPhaseData[]): (ProjectStageData & { phaseKey: string })[] => {
  return phases.map(phase => ({
    stage: phase.phaseName,
    status: convertPhaseStatus(phase.status, phase.date),
    date: formatPhaseDate(phase.date),
    description: `${phase.phaseName}${phase.date ? '' : '（待定）'}`,
    phaseKey: phase.phaseKey // 保留 phaseKey 用于接口调用
  }));
};

/**
 * 转换阶段状态
 * @param status 接口返回的状态
 * @param date 日期
 * @returns 组件需要的状态
 */
const convertPhaseStatus = (status: 'overdue' | 'normal', date: string | null): 'overdue' | 'normal' | 'pending' => {
  // 直接返回接口的状态，不根据日期判断
  // 如果接口返回的状态是 normal 或 overdue，就直接使用
  // 只有当没有明确状态时才返回 pending
  return status;
};

/**
 * 格式化阶段日期
 * @param date 日期字符串
 * @returns 格式化后的日期
 */
const formatPhaseDate = (date: string | null): string => {
  if (!date) return '待定';

  try {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '年').replace(/年(\d+)年/, '年$1月') + '日';
  } catch (error) {
    return date;
  }
};



/**
 * 通过项目编码获取项目详情数据 - 调用真实接口
 * @param projectCode 项目编码
 * @returns Promise<ProjectDetailViewData>
 */
export const getProjectDetailsByCode = async (projectCode: string): Promise<ProjectDetailViewData> => {
  try {
    const response = await http.request<ApiResponse<ApiProjectDetailData>>(
      "get",
      `/project/getProjectDetails/${projectCode}`
    );

    if (response.status === '0' || response.status === 'success') {
      const apiData = response.data;

      // 将接口返回的数据映射到组件需要的格式 - 根据最新字段调整
      const mappedData: ProjectDetailViewData = {
        projectName: apiData.projectName || '',
        projectCode: apiData.projectCode || projectCode,
        approvalNumber: apiData.lxSn || '', // 立项批复文号
        location: `${apiData.prov || ''}${apiData.city || ''}`, // 省分+地市
        constructionMethod: apiData.isPurchase || '', // 建设方式
        constructionNature: apiData.bulidScene || apiData.buildScene || '', // 建设性质
        fundingSource: apiData.touziSubject || '', // 投资主体
        currentStage: apiData.projectState || apiData.proState || '', // 项目当前阶段
        availableBudget: apiData.lxTouZi || '0', // 立项批复总投资（元）
        constructionUnit: apiData.remandUnit || '', // 需求单位
        basicConstructionCode: apiData.legalSubject || '', // 法人主体
        constructionUnitMethod: apiData.projectTrial || '', // 项目审批方式
        projectType: apiData.manageType || '', // 项目管理类别
        isGeneralContract: apiData.postDisaster || '', // 项目专项标识
        projectManager: apiData.managerName || '', // 项目经理姓名
        projectManagerMethod: apiData.managerEmail || '', // 项目经理邮箱前缀
        constructionContent: apiData.lxBulidContent || '', // 立项建设规模信息
        constructionDetails: apiData.mainbuildContent || apiData.sjBulidContent || '', // 主要建设内容
        designBudget: apiData.sjTouZi || '0', // 设计批复总投资（元）
        currentBudget: apiData.kyTouZi || '0', // 可研批复总投资（元）
        newBudget: apiData.finalAccTouzi || '0', // 终验验收总投资（元）
        totalBudget: apiData.firstAccTouzi || '0' // 初验验收总投资（元）
      };

      return mappedData;
    } else {
      throw new Error(response.msg || '获取项目详情失败');
    }
  } catch (error) {
    console.error('获取项目详情失败:', error);
    // 返回默认数据，避免页面崩溃
    return {
      projectName: "项目详情加载失败",
      projectCode: projectCode,
      approvalNumber: "",
      location: "",
      constructionMethod: "",
      constructionNature: "",
      fundingSource: "",
      currentStage: "",
      availableBudget: "0",
      constructionUnit: "",
      basicConstructionCode: "",
      constructionUnitMethod: "",
      projectType: "",
      isGeneralContract: "",
      projectManager: "",
      projectManagerMethod: "",
      constructionContent: "",
      constructionDetails: "",
      designBudget: "0",
      currentBudget: "0",
      newBudget: "0",
      totalBudget: "0"
    };
  }
};

/**
 * 获取项目详情视图数据 - 保持向后兼容（已废弃，建议使用 getProjectDetailsByCode）
 * @param projectId 项目ID
 * @returns Promise<ProjectDetailViewData>
 */
export const getProjectDetailViewData = async (projectId: string): Promise<ProjectDetailViewData> => {
  // 返回默认的项目详情数据，因为模拟数据已移除
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(`ProjectDetailView API: 返回默认数据，建议使用 getProjectDetailsByCode 接口`);
      resolve({
        projectName: "项目名称加载中...",
        projectCode: projectId,
        approvalNumber: "",
        location: "",
        constructionMethod: "",
        constructionNature: "",
        fundingSource: "",
        currentStage: "",
        availableBudget: "0",
        constructionUnit: "",
        basicConstructionCode: "",
        constructionUnitMethod: "",
        projectType: "",
        isGeneralContract: "",
        projectManager: "",
        projectManagerMethod: "",
        constructionContent: "",
        constructionDetails: "",
        designBudget: "0",
        currentBudget: "0",
        newBudget: "0",
        totalBudget: "0"
      });
    }, 300);
  });
};

/**
 * 获取项目计划阶段数据 - 使用真实接口
 * @param projectCode 项目编码
 * @returns Promise<ProjectStageData[]>
 */
export const getProjectPlannedStages = async (projectCode: string): Promise<ProjectStageData[]> => {
  try {
    const phasesData = await getProjectPhases(projectCode);
    return convertPhasesToStageData(phasesData.planPhases);
  } catch (error) {
    console.error('获取项目计划阶段数据失败:', error);
    return [];
  }
};

/**
 * 获取项目实际阶段数据 - 使用真实接口
 * @param projectCode 项目编码
 * @returns Promise<ProjectStageData[]>
 */
export const getProjectActualStages = async (projectCode: string): Promise<ProjectStageData[]> => {
  try {
    const phasesData = await getProjectPhases(projectCode);
    return convertPhasesToStageData(phasesData.actualPhases);
  } catch (error) {
    console.error('获取项目实际阶段数据失败:', error);
    return [];
  }
};

/**
 * 获取项目阶段数据（兼容旧版本）
 * @param projectCode 项目编码
 * @returns Promise<ProjectStageData[]>
 */
export const getProjectStages = async (projectCode: string): Promise<ProjectStageData[]> => {
  // 默认返回计划阶段数据
  return getProjectPlannedStages(projectCode);
};

/**
 * 获取设计信息数据（已废弃，设计信息现在通过 getProjectDetailsByCode 接口获取）
 * @param projectId 项目ID
 * @returns Promise<DesignInfoData>
 */
export const getDesignInfo = async (projectId: string): Promise<DesignInfoData> => {
  // 返回空的设计信息，因为现在通过项目详情接口获取
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(`getDesignInfo: 设计信息现在通过 getProjectDetailsByCode 接口获取`);
      resolve({
        designApprovalNumber: '',
        designApprovalAmount: '',
        designApprovalDate: '',
        designUnit: '',
        designPersonInCharge: '',
        designPersonMethod: '',
        designDate: '',
        designAmount: ''
      });
    }, 300);
  });
};

/**
 * 更新项目信息
 * @param projectId 项目ID
 * @param data 项目数据
 * @returns Promise<{ success: boolean; message: string }>
 */
export const updateProjectDetailView = async (
  projectId: string,
  data: Partial<ProjectDetailViewData>
): Promise<{ success: boolean; message: string }> => {
  // 模拟 API 调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: '项目信息更新成功'
      });
    }, 500);
  });
};

/**
 * 导出项目信息
 * @param projectCode 项目编码
 * @returns Promise<any>
 */
export const exportProjectInfo = async (projectCode: string): Promise<any> => {
  const params = {
    conditions: [
      {
        value: projectCode,
        field: "projectCodeOrName",
        fuzzyable: true,
        operator: "fuzzy"
      }
    ]
  };

  return http.postBlobWithJson("/project/export", params);
};

/**
 * 修改项目阶段时间
 * @param projectCode 项目编码
 * @param phaseKey 阶段键名
 * @param date 修改后的日期
 * @returns Promise<ApiResponse<any>>
 */
export const modifyProjectPhase = async (
  projectCode: string,
  phaseKey: string,
  date: string
): Promise<ApiResponse<any>> => {
  const params = {
    projectCode,
    [phaseKey]: date
  };

  try {
    const response = await http.request<ApiResponse<any>>(
      "post",
      "/project/modifyProjectPhase",
      { data: params }
    );

    return response;
  } catch (error) {
    console.error('修改项目阶段时间失败:', error);
    throw error;
  }
};
