/**
 * 项目信息导出接口测试工具
 * 用于验证新的导出接口是否正常工作
 */

// 模拟测试数据
const testExportData = {
  projectCode: "8EC22ED034567.3710",
  projectId: "7189056",
  projectName: "测试项目导出功能"
};

// 测试导出接口参数构建
function testExportParams() {
  console.log('🧪 测试导出接口参数构建');
  console.log('=====================================');

  // 测试用例1：完整参数
  console.log('📋 测试用例1：完整参数');
  const params1 = buildExportParams(testExportData.projectCode, testExportData.projectId);
  console.log('输入参数:', { 
    projectCode: testExportData.projectCode, 
    projectId: testExportData.projectId 
  });
  console.log('构建结果:', params1);
  console.log('验证结果:', validateExportParams(params1) ? '✅ 通过' : '❌ 失败');

  // 测试用例2：只有项目编码
  console.log('\n📋 测试用例2：只有项目编码');
  const params2 = buildExportParams(testExportData.projectCode);
  console.log('输入参数:', { 
    projectCode: testExportData.projectCode, 
    projectId: undefined 
  });
  console.log('构建结果:', params2);
  console.log('验证结果:', validateExportParams(params2) ? '✅ 通过' : '❌ 失败');

  // 测试用例3：空参数
  console.log('\n📋 测试用例3：空参数');
  const params3 = buildExportParams('');
  console.log('输入参数:', { 
    projectCode: '', 
    projectId: undefined 
  });
  console.log('构建结果:', params3);
  console.log('验证结果:', validateExportParams(params3) ? '✅ 通过' : '❌ 失败');

  return {
    test1: validateExportParams(params1),
    test2: validateExportParams(params2),
    test3: validateExportParams(params3)
  };
}

// 构建导出参数（复制自API函数的逻辑）
function buildExportParams(projectCode, projectId) {
  return {
    projectCode: projectCode,
    projectId: projectId || ""
  };
}

// 验证导出参数
function validateExportParams(params) {
  // 检查参数结构
  if (!params || typeof params !== 'object') {
    console.log('❌ 参数不是对象');
    return false;
  }

  // 检查必填字段
  if (!params.hasOwnProperty('projectCode')) {
    console.log('❌ 缺少projectCode字段');
    return false;
  }

  if (!params.hasOwnProperty('projectId')) {
    console.log('❌ 缺少projectId字段');
    return false;
  }

  // 检查字段类型
  if (typeof params.projectCode !== 'string') {
    console.log('❌ projectCode不是字符串类型');
    return false;
  }

  if (typeof params.projectId !== 'string') {
    console.log('❌ projectId不是字符串类型');
    return false;
  }

  return true;
}

// 测试数据获取逻辑
function testDataRetrieval() {
  console.log('\n🔍 测试数据获取逻辑');
  console.log('=====================================');

  // 模拟不同的数据来源
  const mockScenarios = [
    {
      name: '场景1：父级传递完整数据',
      needsPassedData: {
        projectCode: "8EC22ED034567.3710",
        projectId: "7189056"
      },
      projectData: {},
      props: {}
    },
    {
      name: '场景2：组件内部数据',
      needsPassedData: null,
      projectData: {
        projectCode: "INTERNAL001",
        projectId: "INT001"
      },
      props: {}
    },
    {
      name: '场景3：Props数据',
      needsPassedData: null,
      projectData: {},
      props: {
        projectId: "PROPS001"
      }
    },
    {
      name: '场景4：混合数据（优先级测试）',
      needsPassedData: {
        projectCode: "PASSED001"
      },
      projectData: {
        projectCode: "INTERNAL001",
        projectId: "INT001"
      },
      props: {
        projectId: "PROPS001"
      }
    }
  ];

  const results = [];

  mockScenarios.forEach((scenario, index) => {
    console.log(`\n📋 ${scenario.name}`);
    
    // 模拟数据获取逻辑（复制自组件的逻辑）
    const projectCode = scenario.needsPassedData?.projectCode || scenario.projectData.projectCode;
    const projectIdValue = scenario.needsPassedData?.projectId || scenario.projectData.projectId || scenario.props.projectId;

    console.log('获取结果:', { projectCode, projectId: projectIdValue });
    
    const isValid = projectCode && projectCode.trim() !== '';
    console.log('验证结果:', isValid ? '✅ 有效' : '❌ 无效（缺少项目编码）');
    
    results.push({
      scenario: scenario.name,
      projectCode,
      projectId: projectIdValue,
      isValid
    });
  });

  return results;
}

// 测试接口调用模拟
function testApiCall() {
  console.log('\n🌐 测试接口调用模拟');
  console.log('=====================================');

  const testCases = [
    {
      name: '正常调用',
      projectCode: "8EC22ED034567.3710",
      projectId: "7189056"
    },
    {
      name: '只有项目编码',
      projectCode: "8EC22ED034567.3710",
      projectId: undefined
    }
  ];

  testCases.forEach(testCase => {
    console.log(`\n📋 ${testCase.name}`);
    console.log('请求参数:', {
      projectCode: testCase.projectCode,
      projectId: testCase.projectId
    });

    // 模拟API调用
    const params = buildExportParams(testCase.projectCode, testCase.projectId);
    console.log('实际发送:', params);
    
    // 模拟HTTP请求
    console.log('HTTP请求模拟:');
    console.log('  POST /project/exportProjectDetails');
    console.log('  Content-Type: application/json');
    console.log('  Body:', JSON.stringify(params, null, 2));
    
    console.log('状态:', '✅ 模拟成功');
  });
}

// 执行所有测试
function runAllTests() {
  console.log('🚀 项目信息导出接口测试');
  console.log('=====================================');

  // 测试参数构建
  const paramTests = testExportParams();
  
  // 测试数据获取
  const dataTests = testDataRetrieval();
  
  // 测试接口调用
  testApiCall();

  // 生成测试报告
  console.log('\n📊 测试报告');
  console.log('=====================================');
  
  const paramTestResults = Object.values(paramTests);
  const paramSuccessCount = paramTestResults.filter(result => result).length;
  console.log(`参数构建测试: ${paramSuccessCount}/${paramTestResults.length} 通过`);
  
  const dataTestResults = dataTests.filter(result => result.isValid);
  console.log(`数据获取测试: ${dataTestResults.length}/${dataTests.length} 有效`);
  
  const overallSuccess = paramSuccessCount === paramTestResults.length && dataTestResults.length > 0;
  console.log(`总体结果: ${overallSuccess ? '✅ 测试通过' : '⚠️ 需要检查'}`);

  return {
    paramTests,
    dataTests,
    overallSuccess
  };
}

// 执行测试
const testResults = runAllTests();

// 导出测试数据供调试使用
if (typeof window !== 'undefined') {
  window.exportTestData = testExportData;
  window.exportTestResults = testResults;
  console.log('\n💡 提示: 测试数据已保存到 window.exportTestData 和 window.exportTestResults');
}

console.log('\n🎯 测试完成！');
console.log('请在项目详情页面点击"导出项目信息"按钮验证实际功能。');

// 导出供其他模块使用
export { testExportData, buildExportParams, validateExportParams, runAllTests };
