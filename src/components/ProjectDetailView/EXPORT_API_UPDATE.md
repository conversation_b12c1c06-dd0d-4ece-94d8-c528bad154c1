# 项目信息导出接口更新说明

## 🎯 **更新内容**

根据需求，已将项目信息导出接口从原来的接口更换为新的接口。

## 🔄 **接口变更对比**

### **原接口**
- **接口地址**：`/project/export`
- **请求方法**：`postBlobWithJson`
- **参数格式**：
```javascript
{
  conditions: [
    {
      value: projectCode,
      field: "projectCodeOrName",
      fuzzyable: true,
      operator: "fuzzy"
    }
  ]
}
```

### **新接口**
- **接口地址**：`/project/exportProjectDetails`
- **请求方法**：`postBlobWithJson`
- **参数格式**：
```javascript
{
  projectCode: "8EC22ED034567.3710", // 父级传递的项目编码
  projectId: "7189056"               // 父级传递的项目ID
}
```

## ✅ **代码修改详情**

### **1. API函数更新**
**文件**：`src/components/ProjectDetailView/api.ts`

**修改前**：
```javascript
export const exportProjectInfo = async (projectCode: string): Promise<any> => {
  const params = {
    conditions: [
      {
        value: projectCode,
        field: "projectCodeOrName",
        fuzzyable: true,
        operator: "fuzzy"
      }
    ]
  };

  return http.postBlobWithJson("/project/export", params);
};
```

**修改后**：
```javascript
export const exportProjectInfo = async (projectCode: string, projectId?: string): Promise<any> => {
  const params = {
    projectCode: projectCode,
    projectId: projectId || ""
  };

  return http.postBlobWithJson("/project/exportProjectDetails", params);
};
```

### **2. 组件调用更新**
**文件**：`src/components/ProjectDetailView/index.vue`

**修改前**：
```javascript
const handleExportInfo = async () => {
  try {
    // 获取项目编码
    const projectCode = props.needsPassedData?.projectCode || projectData.value.projectCode;

    if (!projectCode) {
      ElMessage.warning('项目编码为空，无法导出项目信息');
      return;
    }

    // 调用导出接口
    await exportProjectInfo(projectCode);

    ElMessage.success('项目信息导出成功');
  } catch (error) {
    console.error('导出项目信息失败:', error);
    ElMessage.error('导出项目信息失败');
  }
};
```

**修改后**：
```javascript
const handleExportInfo = async () => {
  try {
    // 获取项目编码和项目ID
    const projectCode = props.needsPassedData?.projectCode || projectData.value.projectCode;
    const projectIdValue = props.needsPassedData?.projectId || projectData.value.projectId || props.projectId;

    if (!projectCode) {
      ElMessage.warning('项目编码为空，无法导出项目信息');
      return;
    }

    console.log('导出项目信息参数:', { projectCode, projectId: projectIdValue });

    ElMessage.info('正在导出项目信息，请稍候...');

    // 调用导出接口，传递项目编码和项目ID
    await exportProjectInfo(projectCode, projectIdValue);

    ElMessage.success('项目信息导出成功');
  } catch (error) {
    console.error('导出项目信息失败:', error);
    ElMessage.error('导出项目信息失败');
  }
};
```

## 📋 **参数获取逻辑**

### **项目编码 (projectCode)**
获取优先级：
1. `props.needsPassedData?.projectCode` - 父级传递的项目编码
2. `projectData.value.projectCode` - 组件内部的项目编码

### **项目ID (projectId)**
获取优先级：
1. `props.needsPassedData?.projectId` - 父级传递的项目ID
2. `projectData.value.projectId` - 组件内部的项目ID
3. `props.projectId` - 组件props中的项目ID

## 🧪 **测试验证**

### **测试用例**
```javascript
// 测试数据示例
const testData = {
  projectCode: "8EC22ED034567.3710",
  projectId: "7189056"
};

// 调用导出接口
await exportProjectInfo(testData.projectCode, testData.projectId);
```

### **预期请求**
```javascript
// HTTP请求
POST /project/exportProjectDetails
Content-Type: application/json

{
  "projectCode": "8EC22ED034567.3710",
  "projectId": "7189056"
}
```

## 🔧 **使用方法**

### **1. 确保数据传递**
在调用ProjectDetailView组件时，确保传递了正确的项目信息：
```javascript
// 父组件传递数据
const passedData = {
  projectCode: "8EC22ED034567.3710",
  projectId: "7189056",
  // ... 其他数据
};

// 使用组件
<ProjectDetailView :needs-passed-data="passedData" />
```

### **2. 点击导出按钮**
用户在项目详情页面点击"导出项目信息"按钮，系统会：
1. 自动获取项目编码和项目ID
2. 调用新的导出接口
3. 下载导出的文件

## ⚠️ **注意事项**

### **1. 参数必填性**
- `projectCode` 是必填参数，如果为空会提示错误
- `projectId` 是可选参数，如果为空会传递空字符串

### **2. 错误处理**
- 接口调用失败时会显示错误提示
- 控制台会输出详细的错误信息用于调试

### **3. 数据来源**
- 优先使用父级传递的数据
- 如果父级没有传递，使用组件内部的数据
- 确保数据的准确性和完整性

## 🚀 **部署状态**

- ✅ **接口地址更新**：已更换为 `/project/exportProjectDetails`
- ✅ **参数格式调整**：使用新的参数格式
- ✅ **数据获取优化**：支持多种数据来源
- ✅ **错误处理完善**：提供详细的错误提示
- ✅ **日志输出**：便于调试和问题排查

## 📊 **兼容性说明**

### **向后兼容**
- 保持了原有的函数签名兼容性
- 新增的 `projectId` 参数为可选参数
- 不会影响现有的调用方式

### **数据获取**
- 支持多种数据来源的优先级处理
- 确保在不同场景下都能获取到正确的参数

## 🎉 **总结**

项目信息导出接口已成功更新：

- ✅ **接口地址**：`/project/exportProjectDetails`
- ✅ **参数格式**：`{ projectCode, projectId }`
- ✅ **数据获取**：支持父级传递和组件内部数据
- ✅ **错误处理**：完善的错误提示和日志输出
- ✅ **用户体验**：保持原有的操作流程

用户现在可以正常使用"导出项目信息"功能，系统会使用新的接口和参数格式进行导出操作。
